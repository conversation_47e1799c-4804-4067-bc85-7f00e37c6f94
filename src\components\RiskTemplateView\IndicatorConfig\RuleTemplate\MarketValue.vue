<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone, isNone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject, MarketValueConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';
import { DefaultRiskParamCreator } from '../../DefaultRiskParamCreator';

import {
  AlertType,
  AlertTypes,
  ExpressionType,
  MarketValueTypes,
  RiskStatisticsTypes,
} from '@/enum/riskc';

const localExpressionTypes = [
  ExpressionType.GreaterThan,
  ExpressionType.GreaterEqual,
  ExpressionType.LessThan,
  ExpressionType.LessEqual,
];

const mountExpressionTypes = [ExpressionType.GreaterThan, ExpressionType.GreaterEqual];

const localAlertTypes = [AlertType.Ignore, AlertType.Warning, AlertType.Prevention];
const localMarketValueTypes = [...MarketValueTypes];

/**
 * 市值
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  riskStatisticsType: number;
  marketValueType: number;
  paramAlert: MarketValueConfig;
  paramBlock: MarketValueConfig;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null | undefined;
}>();

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const definedActions = computed(() => {
  const { paramAlert, paramBlock } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramBlock];
});

const rules = {
  actions: [
    { required: true, message: '请选择比较符', trigger: 'blur', validator: validateActions },
  ],
  riskStatisticsType: [{ required: true, message: '请设置完整的条件', trigger: 'blur' }],
};

function makeFormItemLabel(alertType: number) {
  return renderLabel(alertType, AlertTypes) + '设置';
}

function validateActions(rule: any, value: any, callback: any) {
  const failed = definedActions.value.some(item => {
    const { expression, value, alertType } = item;
    if (isNone(expression) || isNone(value) || isNone(alertType)) {
      return true;
    } else {
      return false;
    }
  });
  if (failed) {
    callback(new Error('请设置完善的风控条件'));
  } else {
    callback();
  }
}

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = handleInputChange(newValue);
  },
  { immediate: true },
);

function handleInputChange(newValue: RuleInnerSetting | null | undefined) {
  return newValue && JSON.stringify(newValue) != '{}'
    ? deepClone(newValue)
    : createEmptyRiskParam();
}

function createEmptyRiskParam(): RuleInnerSetting {
  return DefaultRiskParamCreator[IdcComponentNameDef.MarketValue]();
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.MarketValue](localRuleSetting.value);
}

function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" :rules="rules" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置" prop="riskStatisticsType">
          <el-select
            v-model="localRuleSetting.riskStatisticsType"
            style="width: 100px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in RiskStatisticsTypes"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="localRuleSetting.marketValueType"
            style="width: 200px; margin-left: 10px"
            @change="handleParamHotChange"
          >
            <el-option
              v-for="(item, idx) in localMarketValueTypes"
              :key="idx"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item :label="makeFormItemLabel(item.alertType)" prop="marketValueType">
            <div w-full flex aic gap-10>
              <label class="placed-label">当净值</label>
              <el-select
                v-model="item.navExpression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in localExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="3"
                :step="0.001"
                :min="0"
                v-model="item.nav"
                @change="handleParamHotChange"
                style="width: 100px"
              ></el-input-number>
              <label class="placed-label">时，该指标</label>
            </div>
          </el-form-item>
          <el-form-item label="" prop="marketValueType">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in mountExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 80px"
              ></el-input-number>
              <label class="placed-label">万元时</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in localAlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
