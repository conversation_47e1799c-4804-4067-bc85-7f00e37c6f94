import type { CustomColumn } from '@/types';
import { OrderStatusEnum, PositionEffectEnum, TradeDirectionEnum, AssetTypeEnum } from '@/enum';
import {
  renderLabel,
  renderLabelCol,
  defaultCol,
  thousandsCol,
  thousandsIntCol,
  timestampCol,
  formatNumber,
  formatDateTime,
  thousands,
  thousandsInt,
  renderNavCol,
  percentageCol,
  formatPercent,
} from '@/script/formatter';
import {
  BusinessFlagEnum,
  type OrderInfo,
  type PositionInfo,
  type TradeRecordInfo,
  type EquityInfo,
} from '../../../../../../xtrade-sdk/dist';

// 订单状态颜色函数
const getOrderStatusColor = (value: number): string => {
  switch (value) {
    case OrderStatusEnum.全成:
      return 'c-[var(--g-green)]';
    case OrderStatusEnum.已撤:
    case OrderStatusEnum.已驳回:
    case OrderStatusEnum.废单:
      return 'c-[var(--g-red)]';
    case OrderStatusEnum.部分成交:
    case OrderStatusEnum.部分成交撤单:
      return 'c-[var(--g-orange)]';
    default:
      return 'c-[var(--g-white)]';
  }
};

// 通用列定义 - 单独导出每个列定义
export const instrumentCol: CustomColumn<any> = {
  key: 'instrument',
  title: '代码',
  width: 150,
  sortable: true,
};

export const instrumentNameCol: CustomColumn<any> = {
  key: 'instrumentName',
  title: '名称',
  width: 160,
  sortable: true,
};

export const directionCol: CustomColumn<any> = {
  key: 'direction',
  title: '方向',
  width: 140,
  sortable: true,
  cellRenderer: (params: any) => renderLabelCol(params, TradeDirectionEnum, { color: {} }),
  textRenderer: (cellData: any) => renderLabel(cellData, TradeDirectionEnum),
};

export const positionEffectCol: CustomColumn<any> = {
  key: 'positionEffect',
  title: '开平',
  width: 120,
  sortable: true,
  cellRenderer: (params: any) => renderLabelCol(params, PositionEffectEnum),
  textRenderer: (cellData: any) => renderLabel(cellData, PositionEffectEnum),
};

export const assetTypeCol: CustomColumn<any> = {
  key: 'assetType',
  title: '资产类型',
  width: 200,
  sortable: true,
  cellRenderer: (params: any) => renderLabelCol(params, AssetTypeEnum),
  textRenderer: (cellData: any) => renderLabel(cellData, AssetTypeEnum),
};

export const tradingDayCol: CustomColumn<any> = {
  key: 'tradingDay',
  title: '交易日',
  width: 200,
  sortable: true,
};

export const accountNameCol: CustomColumn<any> = {
  key: 'accountName',
  title: '账号名称',
  width: 160,
  sortable: true,
};

export const userNameCol: CustomColumn<any> = {
  key: 'userName',
  title: '发起人',
  width: 180,
  sortable: true,
};

export const tradedPriceCol: CustomColumn<any> = {
  key: 'tradedPrice',
  title: '成交价',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: defaultCol,
  textRenderer: val => formatNumber(val),
};

export const tradeTimeCol: CustomColumn<any> = {
  key: 'tradeTime',
  title: '成交时间',
  width: 200,
  sortable: true,
};

export const exchangeOrderIdCol: CustomColumn<any> = {
  key: 'exchangeOrderId',
  title: '报单编号',
  width: 200,
  sortable: true,
};

export const marketValueCol: CustomColumn<any> = {
  key: 'marketValue',
  title: '市值',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const frozenMarginCol: CustomColumn<any> = {
  key: 'frozenMargin',
  title: '冻结资金',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const closeProfitCol: CustomColumn<any> = {
  key: 'closeProfit',
  title: '平仓盈亏',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: params => thousandsCol(params, { color: {} }),
  textRenderer: thousands,
};

export const commissionCol: CustomColumn<any> = {
  key: 'commission',
  title: '手续费',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const inMoneyCol: CustomColumn<any> = {
  key: 'inMoney',
  title: '入金',
  width: 200,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const outMoneyCol: CustomColumn<any> = {
  key: 'outMoney',
  title: '出金',
  width: 200,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const createTimeCol: CustomColumn<any> = {
  key: 'createTime',
  title: '创建时间',
  width: 200,
  sortable: true,
  cellRenderer: params => timestampCol(params, 'hh:mm:ss'),
  textRenderer: val => formatDateTime(val, 'hh:mm:ss'),
};

// 新增的常用列定义
export const orgNameCol: CustomColumn<any> = {
  key: 'orgName',
  title: '机构',
  width: 120,
  sortable: true,
};

export const brokerNameCol: CustomColumn<any> = {
  key: 'brokerName',
  title: '经纪商',
  width: 120,
  sortable: true,
};

// 订单相关列定义 - 单独导出
export const orderStatusCol: CustomColumn<OrderInfo> = {
  key: 'orderStatus',
  title: '订单状态',
  width: 200,
  sortable: true,
  cellRenderer: ({ cellData }) => {
    const colorClass = getOrderStatusColor(cellData);
    return <span class={colorClass}>{OrderStatusEnum[cellData]}</span>;
  },
  textRenderer: val => renderLabel(val, OrderStatusEnum),
};

export const businessFlagCol: CustomColumn<OrderInfo> = {
  key: 'businessFlag',
  title: '交易方式',
  width: 200,
  sortable: true,
  cellRenderer: params => renderLabelCol(params, BusinessFlagEnum),
  textRenderer: cellData => renderLabel(cellData, BusinessFlagEnum),
};

export const orderPriceCol: CustomColumn<OrderInfo> = {
  key: 'orderPrice',
  title: '委托价',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: defaultCol,
  textRenderer: val => formatNumber(val),
};

export const volumeOriginalCol: CustomColumn<OrderInfo> = {
  key: 'volumeOriginal',
  title: '委托量',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsIntCol,
  textRenderer: thousandsInt,
};

export const tradedVolumeCol: CustomColumn<OrderInfo> = {
  key: 'tradedVolume',
  title: '成交量',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsIntCol,
  textRenderer: thousandsInt,
};

export const orderTimeCol: CustomColumn<OrderInfo> = {
  key: 'orderTime',
  title: '报单时间',
  width: 160,
  sortable: true,
};

export const fundNameCol: CustomColumn<any> = {
  key: 'fundName',
  title: '产品',
  width: 150,
  sortable: true,
};

export const foreignCol: CustomColumn<OrderInfo> = {
  key: 'foreign',
  title: '外来单',
  width: 180,
  sortable: true,
};

export const forceCloseCol: CustomColumn<OrderInfo> = {
  key: 'forceClose',
  title: '强平',
  width: 160,
  sortable: true,
};

export const remarkCol: CustomColumn<any> = {
  key: 'remark',
  title: '备注',
  width: 150,
  sortable: true,
};

// 持仓相关列定义 - 单独导出
export const yesterdayPositionCol: CustomColumn<PositionInfo> = {
  key: 'yesterdayPosition',
  title: '昨仓',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsIntCol,
  textRenderer: thousandsInt,
};

export const todayPositionCol: CustomColumn<PositionInfo> = {
  key: 'todayPosition',
  title: '今仓',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsIntCol,
  textRenderer: thousandsInt,
};

export const frozenVolumeCol: CustomColumn<PositionInfo> = {
  key: 'frozenVolume',
  title: '冻结仓数',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsIntCol,
  textRenderer: thousandsInt,
};

export const avgPriceCol: CustomColumn<PositionInfo> = {
  key: 'avgPrice',
  title: '持仓均价',
  width: 160,
  align: 'right',
  cellRenderer: defaultCol,
  textRenderer: val => formatNumber(val),
};

export const floatProfitCol: CustomColumn<PositionInfo> = {
  key: 'floatProfit',
  title: '浮动盈亏',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: params => thousandsCol(params, { color: {} }),
  textRenderer: thousands,
};

export const usedCommissionCol: CustomColumn<PositionInfo> = {
  key: 'usedCommission',
  title: '手续费',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const usedMarginCol: CustomColumn<PositionInfo> = {
  key: 'usedMargin',
  title: '保证金',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const updateTimeCol: CustomColumn<any> = {
  key: 'updateTime',
  title: '更新时间',
  width: 200,
  sortable: true,
  cellRenderer: params => timestampCol(params, 'hh:mm:ss'),
  textRenderer: val => formatDateTime(val, 'hh:mm:ss'),
};

// 成交记录相关列定义 - 单独导出
export const volumeCol: CustomColumn<TradeRecordInfo> = {
  key: 'volume',
  title: '成交量',
  width: 160,
  sortable: true,
  align: 'right',
  cellRenderer: defaultCol,
  textRenderer: val => formatNumber(val, { fix: 0 }),
};

export const tradeIdCol: CustomColumn<TradeRecordInfo> = {
  key: 'tradeId',
  title: '成交编号',
  width: 150,
  sortable: true,
};

// 权益相关列定义 - 单独导出
export const balanceCol: CustomColumn<any> = {
  key: 'balance',
  title: '权益',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const availableCol: CustomColumn<any> = {
  key: 'available',
  title: '可用资金',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const preBalanceCol: CustomColumn<any> = {
  key: 'preBalance',
  title: '昨日权益',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const risePercentCol: CustomColumn<any> = {
  key: 'risePercent',
  title: '涨跌幅',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: percentageCol,
  textRenderer: formatPercent,
};

export const positionProfitCol: CustomColumn<any> = {
  key: 'positionProfit',
  title: '浮动盈亏(￥)',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: (params: any) => thousandsCol(params, { color: {} }),
  textRenderer: thousands,
};

export const marginCol: CustomColumn<any> = {
  key: 'margin',
  title: '占用保证金',
  width: 200,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const fundShareCol: CustomColumn<EquityInfo> = {
  key: 'fundShare',
  title: '份额',
  width: 200,
  align: 'right',
  cellRenderer: thousandsCol,
  textRenderer: thousands,
};

export const navCol: CustomColumn<any> = {
  key: 'nav',
  title: '净值',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: renderNavCol,
  textRenderer: (val: any) => formatNumber(val, { fix: 4 }),
};

export const dayProfitCol: CustomColumn<EquityInfo> = {
  key: 'dayProfit',
  title: '收益',
  width: 200,
  sortable: true,
  align: 'right',
  cellRenderer: (params: any) => thousandsCol(params, { color: {} }),
  textRenderer: thousands,
};
