<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition, RowAction } from '@/types';
import { isOrderCancelable, OrderStatusEnum, TradeDirectionEnum } from '@/enum';
import { RecordService, TradingService } from '@/api';
import { putRow } from '@/script';
import type { LegacyFundInfo, SocketDataPackage, OrderInfo } from '../../../../../xtrade-sdk/dist';
import {
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
  accountNameCol,
} from './shared/columnDefinitions';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
  hasAction?: boolean;
}>();

// 基础列定义
const baseColumns = [
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
] as ColumnDefinition<OrderInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.unshift(accountNameCol as any);
  }
  return cols;
});

// 行操作
const rowActions: RowAction<OrderInfo>[] = [
  {
    label: '撤单',
    show: (row: OrderInfo) => isOrderCancelable(row.orderStatus),
    onClick: (row: OrderInfo) => {
      TradingService.cancelOrder(row.id);
      ElMessage.success('已发送撤单请求');
    },
    type: 'var(--g-red)',
  },
];

// 过滤委托状态选项
const filterOrderStatuses = [
  { label: '全部', value: 0 },
  { label: '未完成', value: 1 },
  { label: '废单', value: 2 },
  { label: '已完成', value: 3 },
  { label: '部分成交', value: 4 },
  { label: '已撤销', value: 5 },
];

// 过滤委托状态
const orderStatus = shallowRef(0);

// 委托数据
const orders = shallowRef<OrderInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

const customFilter = (item: OrderInfo) => {
  if (!orderStatus.value) {
    return true;
  }
  if (orderStatus.value === 1) {
    return isOrderCancelable(item.orderStatus);
  }
  if (orderStatus.value === 2) {
    return item.orderStatus === OrderStatusEnum.废单;
  }
  if (orderStatus.value === 3) {
    return [OrderStatusEnum.全成, OrderStatusEnum.已撤, OrderStatusEnum.部分成交撤单].includes(
      item.orderStatus,
    );
  }
  if (orderStatus.value === 4) {
    return [OrderStatusEnum.部分成交, OrderStatusEnum.部分成交撤单].includes(item.orderStatus);
  }
  if (orderStatus.value === 5) {
    return [OrderStatusEnum.已撤, OrderStatusEnum.部分成交撤单].includes(item.orderStatus);
  }
  return true;
};

// 监听账户/产品变化，重新获取委托数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchOrders();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchOrders();
  }
  TradingService.subscribeOrderChange(handleOrderChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribeOrderChange(handleOrderChange);
});

/** 监听委托变化 */
const handleOrderChange = (data: SocketDataPackage<OrderInfo>) => {
  const { body } = data;
  if (body) {
    putRow(body, orders);
  }
};

// 实际应用中的数据获取函数
const fetchOrders = async () => {
  if (!activeItem) return;
  const data = await RecordService.getTodayOrders(activeItem.id);
  orders.value = data;
};

// 撤销选中的委托
const cancelSelectedOrders = () => {
  const selectedRows = tableRef.value?.selectedRows ?? [];

  if (selectedRows.length === 0) {
    ElMessage.warning('请选择委托');
    return;
  }

  const selectedCancelableRows = selectedRows.filter(row => isOrderCancelable(row.orderStatus));

  if (selectedCancelableRows.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }

  selectedCancelableRows.forEach(row => {
    TradingService.cancelOrder(row.id);
  });
  ElMessage.success('已发送撤单请求');
};

// 撤销全部委托
const cancelAllOrders = () => {
  const cancelableOrders = orders.value.filter(order => isOrderCancelable(order.orderStatus));
  if (cancelableOrders.length === 0) {
    ElMessage.warning('无可撤销委托');
    return;
  }
  cancelableOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤单请求');
};

// 撤销买单
const cancelBuyOrders = () => {
  const buyOrders = orders.value.filter(
    order => isOrderCancelable(order.orderStatus) && order.direction === TradeDirectionEnum.买入,
  );
  if (buyOrders.length === 0) {
    ElMessage.warning('无可撤销买单');
    return;
  }
  buyOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤买单请求');
};

// 撤销卖单
const cancelSellOrders = () => {
  const sellOrders = orders.value.filter(
    order => isOrderCancelable(order.orderStatus) && order.direction === TradeDirectionEnum.卖出,
  );
  if (sellOrders.length === 0) {
    ElMessage.warning('无可撤销卖单');
    return;
  }
  sellOrders.forEach(order => {
    TradingService.cancelOrder(order.id);
  });
  ElMessage.success('已发送撤卖单请求');
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns
    :data="orders"
    :row-actions
    :customFilter
    select
    fixed
  >
    <template #left>
      <div flex aic>
        <span mr-8>订单状态:</span>
        <div w-120 mr-16>
          <el-select v-model="orderStatus" placeholder="选择状态" size="small">
            <el-option
              v-for="status in filterOrderStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </div>
      </div>
    </template>
    <template #actions>
      <div v-if="hasAction" class="actions" flex aic>
        <el-button @click="fetchOrders" size="small" color="var(--g-primary)">刷新</el-button>
        <el-button
          @click="cancelSelectedOrders"
          size="small"
          color="var(--g-primary)"
          :disabled="!tableRef?.selectedRows.length"
        >
          撤勾选
        </el-button>
        <el-button @click="cancelAllOrders" size="small" color="var(--g-primary)">全撤</el-button>
        <el-button @click="cancelBuyOrders" size="small" color="var(--g-primary)">撤买单</el-button>
        <el-button @click="cancelSellOrders" size="small" color="var(--g-primary)">
          撤卖单
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
