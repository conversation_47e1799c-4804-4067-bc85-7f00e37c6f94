<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import type { HistoryQueryOptions, LegacyFundInfo, PositionInfo } from '../../../../../xtrade-sdk';
import { IdentityType, Repos } from '../../../../../xtrade-sdk/dist';
import {
  instrumentCol,
  instrumentNameCol,
  assetTypeCol,
  directionCol,
  yesterdayPositionCol,
  todayPositionCol,
  frozenVolumeCol,
  avgPriceCol,
  marketValueCol,
  floatProfitCol,
  closeProfitCol,
  usedCommissionCol,
  usedMarginCol,
  updateTimeCol,
  accountNameCol,
} from './shared/columnDefinitions';
import DateRangePicker from './shared/DateRangePicker.vue';
import { getDefaultDateRange } from '@/script';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns = [
  instrumentCol,
  instrumentNameCol,
  assetTypeCol,
  directionCol,
  yesterdayPositionCol,
  todayPositionCol,
  frozenVolumeCol,
  avgPriceCol,
  marketValueCol,
  floatProfitCol,
  closeProfitCol,
  usedCommissionCol,
  usedMarginCol,
  updateTimeCol,
] as ColumnDefinition<PositionInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(2, 0, accountNameCol as any);
  }
  return cols;
});

// 历史持仓数据
const historyPositions = shallowRef<PositionInfo[]>([]);

// 日期范围
const dateRange = shallowRef<[string, string]>(getDefaultDateRange());

// 获取历史持仓数据
const fetchHistoryPositions = async () => {
  if (!activeItem) return;

  // 构建查询参数
  const options = {
    pageNo: 1,
    pageSize: 100000,
  } as HistoryQueryOptions;

  // 根据类型设置查询参数
  if (type === 'account') {
    options.account_id = activeItem.id;
  } else {
    options.fund_id = activeItem.id;
  }

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const identityType = type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value;
  const response = await recordsRepo.QueryHistoryPositions(identityType, options);

  if (response && response.data) {
    historyPositions.value = response.data.list;
  } else {
    historyPositions.value = [];
  }
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史持仓数据');
};

onMounted(() => {
  if (activeItem) {
    fetchHistoryPositions();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryPositions();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, (newValue, oldValue) => {
  if (newValue[0] !== oldValue[0] || newValue[1] !== oldValue[1]) {
    fetchHistoryPositions();
  }
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'tradingDay', order: TableV2SortOrder.DESC }"
    :columns
    :data="historyPositions"
    identity="instrument"
    fixed
  >
    <template #left>
      <DateRangePicker v-model="dateRange" />
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchHistoryPositions" size="small" color="var(--g-primary)">
          查询
        </el-button>
        <el-button @click="exportData" size="small">导出</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
