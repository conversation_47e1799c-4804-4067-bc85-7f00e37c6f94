<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import type { LegacyFundInfo, EquityInfo } from '../../../../../xtrade-sdk';
import { IdentityType, Repos } from '../../../../../xtrade-sdk/dist';
import {
  tradingDayCol,
  balanceCol,
  marketValueCol,
  availableCol,
  preBalanceCol,
  frozenMarginCol,
  risePercentCol,
  closeProfitCol,
  positionProfitCol,
  inMoneyCol,
  outMoneyCol,
  commissionCol,
  marginCol,
  fundShareCol,
  renderNavCol,
  dayProfitCol,
} from './shared/columnDefinitions';
import DateRangePicker from './shared/DateRangePicker.vue';
import { getDefaultDateRange } from '@/script';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns = [
  tradingDayCol,
  balanceCol,
  marketValueCol,
  availableCol,
  preBalanceCol,
  frozenMarginCol,
  risePercentCol,
  closeProfitCol,
  positionProfitCol,
  inMoneyCol,
  outMoneyCol,
  commissionCol,
  marginCol,
  fundShareCol,
  renderNavCol,
  dayProfitCol,
] as ColumnDefinition<EquityInfo>;

// 历史权益数据
const historyEquity = shallowRef<EquityInfo[]>([]);

// 日期范围
const dateRange = shallowRef<[string, string]>(getDefaultDateRange());

// 获取历史权益数据
const fetchHistoryEquity = async () => {
  if (!activeItem) return;

  // 构建查询参数
  const options: any = {
    identity_id: activeItem.id,
    identity_type: type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value,
    pageNo: 1,
    pageSize: 100000,
  };

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const response = await recordsRepo.QueryHistoryEquities(options);

  if (response && response.data) {
    historyEquity.value = response.data;
  } else {
    historyEquity.value = [];
  }
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史权益数据');
};

onMounted(() => {
  if (activeItem) {
    fetchHistoryEquity();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryEquity();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, (newValue, oldValue) => {
  if (newValue[0] !== oldValue[0] || newValue[1] !== oldValue[1]) {
    fetchHistoryEquity();
  }
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'tradingDay', order: TableV2SortOrder.DESC }"
      :columns="baseColumns"
      :data="historyEquity"
      flex-1
      min-h-1
      fixed
    >
      <template #left>
        <DateRangePicker v-model="dateRange" />
      </template>
      <template #actions>
        <div class="actions" flex aic>
          <el-button @click="fetchHistoryEquity" size="small" color="var(--g-primary)">
            查询
          </el-button>
          <el-button @click="exportData" size="small">导出</el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
